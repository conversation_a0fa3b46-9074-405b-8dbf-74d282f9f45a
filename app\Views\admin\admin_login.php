<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row justify-content-center align-items-center" style="min-height: 80vh;">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header text-center">
                    <h2 style="color: var(--brand-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        PROMIS Admin Portal
                    </h2>
                    <p style="color: var(--text-secondary); margin: 0;">
                        Project Management Information Systems
                    </p>
                </div>
                
                <div style="padding: var(--spacing-xl);">
                    <?= form_open('admin/login', ['class' => 'admin-login-form']) ?>
                    
                    <?= csrf_field() ?>
                    
                    <div class="form-group">
                        <label for="username" class="form-label">Username or Email</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-input" 
                            value="<?= old('username') ?>"
                            placeholder="Enter your username or email"
                            required
                            autofocus
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Enter your password"
                            required
                        >
                    </div>

                    <div class="form-group">
                        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                            <input 
                                type="checkbox" 
                                id="remember_me" 
                                name="remember_me" 
                                value="1"
                                style="width: auto;"
                            >
                            <label for="remember_me" style="margin: 0; font-weight: normal; cursor: pointer;">
                                Remember me for 30 days
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Sign In to Admin Portal
                        </button>
                    </div>

                    <?= form_close() ?>

                    <div style="text-align: center; margin-top: var(--spacing-lg); padding-top: var(--spacing-lg); border-top: 1px solid #E5E7EB;">
                        <p style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                            Organization User?
                        </p>
                        <a href="<?= base_url('organization/login') ?>" class="btn btn-secondary" style="width: 100%;">
                            Organization Login
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div style="text-align: center; margin-top: var(--spacing-xl); color: var(--text-muted); font-size: 0.875rem;">
                <p>PROMIS v2.0 - Project Management Information Systems</p>
                <p>© 2025 All Rights Reserved</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Login-specific styles */
.admin-login-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-login-form .btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: var(--spacing-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.admin-login-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.admin-login-form .btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid #D1D5DB;
    padding: var(--spacing-md);
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-login-form .btn-secondary:hover {
    background: #E5E7EB;
    color: var(--text-primary);
    text-decoration: none;
}

/* Override template styles for login page */
.sidebar {
    display: none;
}

.main-content {
    margin-left: 0;
}

.header {
    display: none;
}

.content {
    padding: var(--spacing-md);
}

body {
    background: linear-gradient(135deg, #EFF6FF 0%, #F8FAFC 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-6 {
        padding: var(--spacing-md);
    }
    
    .card {
        margin: var(--spacing-md) 0;
    }
}
</style>

<?= $this->endSection() ?>
