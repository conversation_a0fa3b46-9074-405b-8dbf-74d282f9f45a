<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row justify-content-center align-items-center" style="min-height: 80vh;">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header text-center">
                    <h2 style="color: var(--brand-primary); font-weight: 600; margin-bottom: var(--spacing-sm);">
                        Organization Portal
                    </h2>
                    <p style="color: var(--text-secondary); margin: 0;">
                        Project Management Information Systems
                    </p>
                </div>
                
                <div style="padding: var(--spacing-xl);">
                    <!-- Organization Branding Area -->
                    <div style="text-align: center; margin-bottom: var(--spacing-xl); padding: var(--spacing-lg); background: var(--bg-accent); border-radius: var(--radius-md);">
                        <div style="width: 80px; height: 80px; margin: 0 auto var(--spacing-md); background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 700;">
                            ORG
                        </div>
                        <p style="color: var(--text-secondary); font-size: 0.875rem; margin: 0;">
                            Organization User Login
                        </p>
                    </div>

                    <form action="<?= base_url('organization/login') ?>" method="post" class="org-login-form">

                    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                    
                    <div class="form-group">
                        <label for="username" class="form-label">Username or Email</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-input" 
                            value="<?= old('username') ?>"
                            placeholder="Enter your username or email"
                            required
                            autofocus
                        >
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Enter your password"
                            required
                        >
                    </div>

                    <div class="form-group">
                        <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                            <input 
                                type="checkbox" 
                                id="remember_me" 
                                name="remember_me" 
                                value="1"
                                style="width: auto;"
                            >
                            <label for="remember_me" style="margin: 0; font-weight: normal; cursor: pointer;">
                                Remember me for 30 days
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Sign In to Organization Portal
                        </button>
                    </div>

                    </form>

                    <div style="text-align: center; margin-top: var(--spacing-lg); padding-top: var(--spacing-lg); border-top: 1px solid #E5E7EB;">
                        <p style="color: var(--text-muted); font-size: 0.875rem; margin-bottom: var(--spacing-sm);">
                            System Administrator?
                        </p>
                        <a href="<?= base_url('admin/login') ?>" class="btn btn-secondary" style="width: 100%;">
                            Admin Login
                        </a>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div style="text-align: center; margin-top: var(--spacing-xl); color: var(--text-muted); font-size: 0.875rem;">
                <p>PROMIS v2.0 - Project Management Information Systems</p>
                <p>© 2025 All Rights Reserved</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Organization login-specific styles */
.org-login-form .form-input:focus {
    border-color: var(--brand-secondary);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.org-login-form .btn-primary {
    background: var(--gradient-secondary);
    border: none;
    padding: var(--spacing-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.org-login-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.org-login-form .btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid #D1D5DB;
    padding: var(--spacing-md);
    font-weight: 500;
    transition: all 0.3s ease;
}

.org-login-form .btn-secondary:hover {
    background: #E5E7EB;
    color: var(--text-primary);
    text-decoration: none;
}

/* Override template styles for login page */
.sidebar {
    display: none;
}

.main-content {
    margin-left: 0;
}

.header {
    display: none;
}

.content {
    padding: var(--spacing-md);
}

body {
    background: linear-gradient(135deg, #ECFDF5 0%, #F8FAFC 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-6 {
        padding: var(--spacing-md);
    }
    
    .card {
        margin: var(--spacing-md) 0;
    }
}
</style>

<?= $this->endSection() ?>
