<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Admin Authentication Controller
 * 
 * Handles authentication for the PROMIS Admin Portal including:
 * - Admin user login and logout
 * - Organization-specific login
 * - Session management
 * - Remember me functionality
 */
class AdminAuthController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
    }

    /**
     * Display admin login form - GET request
     */
    public function showLoginForm()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('admin_user_id')) {
            return redirect()->to(base_url('admin/dashboard'));
        }

        $data = [
            'title' => 'Admin Login - PROMIS',
            'page_title' => 'Admin Login'
        ];

        return view('admin/admin_login', $data);
    }

    /**
     * Process admin login - POST request
     */
    public function authenticateUser()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token. Please try again.');
        }

        // Validation rules
        $rules = [
            'username' => 'required|min_length[3]',
            'password' => 'required|min_length[4]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $username = $this->request->getPost('username');
        $password = $this->request->getPost('password');
        $rememberMe = $this->request->getPost('remember_me') ? true : false;

        // Find user by username or email
        $user = $this->userModel->where('username', $username)
                                ->orWhere('email', $username)
                                ->where('deleted_at', null)
                                ->first();

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Invalid username or password.');
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            return redirect()->back()->withInput()->with('error', 'Invalid username or password.');
        }

        // Check if user is activated
        if (!$user['is_activated']) {
            return redirect()->back()->withInput()->with('error', 'Your account is not activated. Please contact administrator.');
        }

        // Get organization details
        $organization = $this->organizationModel->find($user['organization_id']);
        
        // Set session variables
        $sessionData = [
            'admin_user_id' => $user['id'],
            'admin_username' => $user['username'],
            'admin_user_name' => $user['name'],
            'admin_user_role' => $user['role'],
            'admin_organization_id' => $user['organization_id'],
            'admin_organization_name' => $organization ? $organization['name'] : 'Unknown',
            'admin_logged_in' => true,
            'admin_last_activity' => time()
        ];
        
        session()->set($sessionData);

        // Update last login
        $this->userModel->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);

        // Handle remember me
        if ($rememberMe) {
            $this->setRememberMeToken($user['id']);
        }

        // Log authentication
        $this->logAuditEvent('login', $user['id'], 'users', $user['id'], [
            'description' => 'Admin user ' . $user['username'] . ' logged in successfully',
            'event_type' => 'authentication'
        ]);

        return redirect()->to(base_url('admin/dashboard'))->with('success', 'Welcome back, ' . $user['name'] . '!');
    }

    /**
     * Display organization-specific login form - GET request
     */
    public function showOrganizationLoginForm()
    {
        // If already logged in, redirect to dashboard
        if (session()->get('admin_user_id')) {
            return redirect()->to(base_url('admin/dashboard'));
        }

        $data = [
            'title' => 'Organization Login - PROMIS',
            'page_title' => 'Organization Login'
        ];

        return view('admin/admin_organization_login', $data);
    }

    /**
     * Process organization user login - POST request
     */
    public function authenticateOrganizationUser()
    {
        // Use same authentication logic as main login
        return $this->authenticateUser();
    }

    /**
     * Logout user - POST request
     */
    public function logoutUser()
    {
        $userId = session()->get('admin_user_id');
        $username = session()->get('admin_username');

        if ($userId) {
            // Log logout event
            $this->logAuditEvent('logout', $userId, 'users', $userId, [
                'description' => 'Admin user ' . $username . ' logged out',
                'event_type' => 'authentication'
            ]);
        }

        // Clear remember me cookie
        $this->clearRememberMeToken();

        // Destroy session
        session()->destroy();

        return redirect()->to(base_url('admin/login'))->with('success', 'You have been logged out successfully.');
    }

    /**
     * Set remember me token
     */
    private function setRememberMeToken($userId)
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = password_hash($token, PASSWORD_ARGON2ID);
        
        // Store token in database (Note: This would require adding remember_token field to users table)
        // For now, we'll use a secure cookie approach
        
        $cookieOptions = [
            'expires' => time() + (30 * 24 * 60 * 60), // 30 days
            'path' => '/',
            'secure' => false, // Set to true in production with HTTPS
            'httponly' => true,
            'samesite' => 'Lax'
        ];
        
        setcookie('admin_remember_token', $token . '|' . $userId, $cookieOptions);
    }

    /**
     * Clear remember me token
     */
    private function clearRememberMeToken()
    {
        if (isset($_COOKIE['admin_remember_token'])) {
            setcookie('admin_remember_token', '', time() - 3600, '/');
        }
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'authentication',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
