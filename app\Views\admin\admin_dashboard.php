<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>

<!-- Dashboard Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Welcome back, <?= esc(session()->get('admin_user_name')) ?>!
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Here's what's happening in your PROMIS system today.
        </p>
    </div>
    <div>
        <!-- Dashboard Controls -->
        <div style="display: flex; gap: var(--spacing-md); align-items: center;">
            <!-- Theme Switcher -->
            <form action="<?= base_url('admin/theme/switch') ?>" method="post" style="display: inline-block;">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                <select name="theme" onchange="this.form.submit()" class="form-input" style="width: auto;">
                    <option value="light" <?= (session()->get('admin_theme') === 'light' || !session()->get('admin_theme')) ? 'selected' : '' ?>>☀️ Light</option>
                    <option value="dark" <?= (session()->get('admin_theme') === 'dark') ? 'selected' : '' ?>>🌙 Dark</option>
                    <option value="auto" <?= (session()->get('admin_theme') === 'auto') ? 'selected' : '' ?>>🔄 Auto</option>
                </select>
            </form>

            <!-- Dashboard Reset -->
            <form action="<?= base_url('admin/dashboard/reset') ?>" method="post" style="display: inline-block;">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
                <button type="submit" class="btn btn-secondary" onclick="return confirm('Reset dashboard to default layout?')" title="Reset Layout">
                    🔄 Reset
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-2xl);">
    
    <!-- Users Stats -->
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['total_users']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Total Users
                </p>
                <p style="color: var(--brand-secondary); margin: 0; font-size: 0.75rem; margin-top: var(--spacing-xs);">
                    <?= number_format($stats['active_users']) ?> active
                </p>
            </div>
            <div style="width: 60px; height: 60px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                👥
            </div>
        </div>
    </div>

    <!-- Organizations Stats -->
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['total_organizations']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Organizations
                </p>
                <p style="color: var(--brand-secondary); margin: 0; font-size: 0.75rem; margin-top: var(--spacing-xs);">
                    <?= number_format($stats['active_organizations']) ?> active
                </p>
            </div>
            <div style="width: 60px; height: 60px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                🏢
            </div>
        </div>
    </div>

    <!-- Projects Stats -->
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--text-primary); font-size: 2rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    <?= number_format($stats['total_projects']) ?>
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    Projects
                </p>
                <p style="color: var(--brand-secondary); margin: 0; font-size: 0.75rem; margin-top: var(--spacing-xs);">
                    <?= number_format($stats['active_projects']) ?> active
                </p>
            </div>
            <div style="width: 60px; height: 60px; background: var(--gradient-accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                📁
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div>
                <h3 style="color: var(--brand-secondary); font-size: 1.25rem; font-weight: 700; margin-bottom: var(--spacing-xs);">
                    Excellent
                </h3>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">
                    System Health
                </p>
                <p style="color: var(--text-muted); margin: 0; font-size: 0.75rem; margin-top: var(--spacing-xs);">
                    All systems operational
                </p>
            </div>
            <div style="width: 60px; height: 60px; background: var(--gradient-secondary); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                ✅
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-xl);">
    
    <!-- Recent Activities -->
    <div class="card">
        <div class="card-header">
            Recent Activities
        </div>
        <div class="activities-list">
            <?php if (!empty($recent_activities)): ?>
                <?php foreach ($recent_activities as $activity): ?>
                    <div style="display: flex; align-items: center; padding: var(--spacing-md) 0; border-bottom: 1px solid #E5E7EB;">
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: var(--bg-accent); display: flex; align-items: center; justify-content: center; margin-right: var(--spacing-md); font-size: 1.125rem;">
                            <?php
                            $icons = [
                                'user' => '👤',
                                'project' => '📁',
                                'organization' => '🏢'
                            ];
                            echo $icons[$activity['type']] ?? '📋';
                            ?>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                                <?= esc($activity['action']) ?>
                            </h4>
                            <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0;">
                                <?= esc($activity['description']) ?>
                            </p>
                        </div>
                        <div style="color: var(--text-muted); font-size: 0.75rem;">
                            <?= esc($activity['time']) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div style="text-align: center; padding: var(--spacing-2xl); color: var(--text-muted);">
                    <p>No recent activities to display.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            Quick Actions
        </div>
        <div style="display: flex; flex-direction: column; gap: var(--spacing-md);">
            <a href="<?= base_url('admin/users/create') ?>" class="btn btn-primary" style="text-decoration: none;">
                👤 Create New User
            </a>
            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary" style="text-decoration: none;">
                📋 Manage Users
            </a>
            <a href="<?= base_url('admin/audit') ?>" class="btn btn-secondary" style="text-decoration: none;">
                🔍 View Audit Trail
            </a>
            <a href="<?= base_url('admin/search') ?>" class="btn btn-secondary" style="text-decoration: none;">
                🔎 Global Search
            </a>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card mt-xl">
    <div class="card-header">
        System Information
    </div>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg);">
        <div>
            <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                Current User
            </h4>
            <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0;">
                <?= esc(session()->get('admin_user_name')) ?> (<?= esc(session()->get('admin_user_role')) ?>)
            </p>
        </div>
        <div>
            <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                Organization
            </h4>
            <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0;">
                <?= esc(session()->get('admin_organization_name')) ?>
            </p>
        </div>
        <div>
            <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                Session Status
            </h4>
            <p style="color: var(--brand-secondary); font-size: 0.75rem; margin: 0;">
                Active
            </p>
        </div>
        <div>
            <h4 style="color: var(--text-primary); font-size: 0.875rem; font-weight: 600; margin-bottom: var(--spacing-xs);">
                PROMIS Version
            </h4>
            <p style="color: var(--text-secondary); font-size: 0.75rem; margin: 0;">
                v2.0
            </p>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
